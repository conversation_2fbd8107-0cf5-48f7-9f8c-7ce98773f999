import { app, BrowserWindow, ipcMain } from "electron";
import path from "path";
import { getEmployerDb } from "./EmployerDbManager";
import { createEmployerDb } from "../file-handlers/createEmployerDb";
import { getMasterDb } from "../../drizzle/adapters/master";
import { employers } from "../../drizzle/schema/master";
import { runMigrationsElectron } from "../../drizzle/migrate-master";
import { eq } from "drizzle-orm";
import { EMPLOYER_DB_EXTENSION, APP_NAME } from "../../constants/file";
import { createClient } from "@libsql/client";
import {
  addExistingEmployer,
  removeEmployer,
  openEmployerFilePicker,
} from "../file-handlers/manageEmployers";

let mainWindow: BrowserWindow | null = null;

// --- BEGIN: Employer DB IPC handlers ---
import { employer } from "../../drizzle/schema/employer";
import {
  openEmployerDb,
  closeEmployerDb,
  isEmployerDbOpen,
  closeAllEmployerDbs,
} from "./EmployerDbManager";
import "../ipc/employer";
import "../ipc/payPeriod";
import "../ipc/payslips";
import "../ipc/payslip-additions";
import "../ipc/payslip-deductions";

// Debug logging handler
ipcMain.handle("debug:log", async (event, message: string) => {
  console.log(`[RENDERER DEBUG] ${message}`);
  return true;
});

import fs from "fs";

function isValidSQLiteFile(filePath: string): boolean {
  try {
    const fd = fs.openSync(filePath, "r");
    const buffer = Buffer.alloc(16);
    fs.readSync(fd, buffer, 0, 16, 0);
    fs.closeSync(fd);
    return buffer.toString() === "SQLite format 3\u0000";
  } catch (err) {
    console.error("[IPC] Error reading file header:", err);
    return false;
  }
}

ipcMain.handle("employerDb:openPersistent", async (event, dbPath) => {
  try {
    if (!dbPath || typeof dbPath !== "string" || !fs.existsSync(dbPath)) {
      console.error("[IPC] Invalid or missing dbPath:", dbPath);
      return { success: false, error: "Invalid or missing dbPath" };
    }
    if (!isValidSQLiteFile(dbPath)) {
      return { success: false, error: "File is not a valid SQLite database" };
    }
    // --- DEVELOPMENT ONLY: Run employer DB migrations automatically ---
    // TODO: Remove this for production (see progress log "Remove auto-migrate on employer DB open")
    const { runMigrationsEmployer } = await import(
      "../../drizzle/migrate-employer"
    );
    await runMigrationsEmployer(dbPath);
    // Validate the employer table before adding to connection map
    const { db } = await openEmployerDb(dbPath, async (db) => {
      await db.select().from(employer).limit(1);
    });
    return { success: true };
  } catch (err: any) {
    // Defensive: close if error
    closeEmployerDb(dbPath);
    console.error("[IPC] Error opening employer DB:", err);
    return { success: false, error: err.message };
  }
});

ipcMain.handle("employerDb:close", async (event, dbPath) => {
  closeEmployerDb(dbPath);
  return { success: true };
});

ipcMain.handle("employerDb:isOpen", async (event, dbPath) => {
  return { isOpen: isEmployerDbOpen(dbPath) };
});

// Handler to get a persistent employer DB connection for other queries
ipcMain.handle("employerDb:getPersistent", async (event, dbPath) => {
  const entry = getEmployerDb(dbPath);
  if (!entry) {
    return { success: false, error: "Employer DB is not open" };
  }
  return { success: true };
});

// Handler to get all employees from a specific employer DB
import { employee } from "../../drizzle/schema/employer/employee.schema";

// IPC handler to get all employees from the employer DB
ipcMain.handle("employerDb:getEmployees", async (event, dbPath) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) {
      return { success: false, error: "Employer DB is not open" };
    }
    const db = entry.db;
    const employees = await db.select().from(employee).all();
    return { success: true, employees };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

// Add Employee
ipcMain.handle(
  "employerDb:addEmployee",
  async (event, dbPath, employeeData) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) {
        return { success: false, error: "Employer DB is not open" };
      }
      const db = entry.db;
      // Insert employee, letting DB assign id if not present
      const [inserted] = await db
        .insert(employee)
        .values(employeeData)
        .returning();
      return { success: true, employee: inserted };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Update Employee
ipcMain.handle(
  "employerDb:updateEmployee",
  async (event, dbPath, employeeData) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) {
        return { success: false, error: "Employer DB is not open" };
      }
      const db = entry.db;
      if (!employeeData.id) {
        return { success: false, error: "Missing employee id for update" };
      }
      const [updated] = await db
        .update(employee)
        .set(employeeData)
        .where(eq(employee.id, employeeData.id))
        .returning();
      if (!updated) {
        return { success: false, error: "Employee not found or not updated" };
      }
      return { success: true, employee: updated };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

ipcMain.handle("employerDb:getPayrolls", async (event, employerId) => {
  // TODO: Lookup file path for employerId using master DB if needed
  // For now, this is a placeholder that returns an empty array
  return [];
});
// --- END: Employer DB IPC handlers ---

// Initialize database and run migrations
async function initializeDatabase() {
  try {
    await runMigrationsElectron();
    console.log("Master database migrations completed successfully");
    await new Promise((resolve) => setTimeout(resolve, 100));
  } catch (error) {
    console.error("Failed to run master database migrations:", error);
    app.quit();
    process.exit(1);
  }
}

// IPC handler for employers list (master DB)
ipcMain.handle("employers:list", async () => {
  const db = getMasterDb();
  const rows = await db.select().from(employers).all();

  // Define interface for removed employers
  interface RemovedEmployer {
    id: string;
    name: string;
    filePath: string;
  }

  // Process employers: check existence and update display names
  // fs and path are already imported at the top of the file, so use those directly.
  const removedEmployers: RemovedEmployer[] = [];
  const updatedEmployers: { id: string; oldName: string; newName: string }[] =
    [];
  const validEmployers: typeof rows = [];

  // Performance optimization: Only do the expensive file scanning if we have a reasonable number of employers
  // For very large lists, just do basic existence checks and display name updates
  const isLargeEmployerList = rows.length > 500;

  // Map to cache file existence checks to avoid redundant file system operations
  const fileExistsCache = new Map<string, boolean>();

  // Function to check if a file exists (with caching)
  const checkFileExists = (filePath: string): boolean => {
    if (fileExistsCache.has(filePath)) {
      return fileExistsCache.get(filePath)!;
    }
    const exists = fs.existsSync(filePath);
    fileExistsCache.set(filePath, exists);
    return exists;
  };

  // Collect all potential employer files in the directories (only for smaller lists)
  const potentialFiles = new Map<string, string>(); // Map of basename -> full path

  if (!isLargeEmployerList) {
    const directoriesToCheck = new Set<string>();

    // First, collect all directories where employer files are stored
    for (const employer of rows) {
      const dirPath = path.dirname(employer.file_path);
      directoriesToCheck.add(dirPath);
    }

    // Scan all directories for potential employer files
    for (const dirPath of directoriesToCheck) {
      try {
        if (checkFileExists(dirPath)) {
          const files = fs.readdirSync(dirPath);
          for (const file of files) {
            if (file.endsWith(EMPLOYER_DB_EXTENSION)) {
              const fullPath = path.join(dirPath, file);
              const baseName = path
                .basename(file, EMPLOYER_DB_EXTENSION)
                .toLowerCase();
              potentialFiles.set(baseName, fullPath);
            }
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dirPath}:`, error);
      }
    }
  }

  // Process each employer
  for (const employer of rows) {
    const fileExists = checkFileExists(employer.file_path);

    // If file doesn't exist at the original path, check if it might have been renamed
    if (!fileExists) {
      let foundMatch = false;

      // Only try to find renamed files for smaller lists
      if (!isLargeEmployerList) {
        // Try to find a potential match based on employer ID or similar name
        const originalDir = path.dirname(employer.file_path);

        // First, try to find a file with a similar name in the same directory
        const potentialMatches: string[] = [];

        // Collect potential matches first to avoid opening connections inside the loop
        for (const [_, filePath] of potentialFiles.entries()) {
          // Skip files that are not in the same directory
          if (path.dirname(filePath) !== originalDir) continue;

          // Check if the file is not already assigned to another employer
          const isAssigned = rows.some(
            (e) => e.file_path === filePath && e.id !== employer.id,
          );
          if (isAssigned) continue;

          potentialMatches.push(filePath);
        }

        // Now check the potential matches (limited to 10 to avoid excessive checking)
        const matchesToCheck = potentialMatches.slice(0, 10);

        for (const filePath of matchesToCheck) {
          try {
            // Open the file and check if it has the same employer ID
            const client = createClient({ url: `file:${filePath}` });
            try {
              const result = await client.execute({
                sql: "SELECT id FROM employer LIMIT 1",
              });

              if (result.rows.length > 0 && result.rows[0].id === employer.id) {
                // Found a match! Update the file path
                console.log(
                  `Found renamed employer file for ${employer.id}: ${filePath} (was: ${employer.file_path})`,
                );

                // Update the file path in the database
                db.update(employers)
                  .set({
                    file_path: filePath,
                    display_name: path.basename(
                      filePath,
                      EMPLOYER_DB_EXTENSION,
                    ),
                    updated_at: new Date(),
                  })
                  .where(eq(employers.id, employer.id))
                  .run()
                  .catch((err) =>
                    console.error(
                      `Error updating file path for employer ${employer.id}:`,
                      err,
                    ),
                  );

                // Update the current record for the response
                employer.file_path = filePath;
                employer.display_name = path.basename(
                  filePath,
                  EMPLOYER_DB_EXTENSION,
                );

                // Add to the list of updated employers
                updatedEmployers.push({
                  id: employer.id,
                  oldName: path.basename(
                    employer.file_path,
                    EMPLOYER_DB_EXTENSION,
                  ),
                  newName: path.basename(filePath, EMPLOYER_DB_EXTENSION),
                });

                foundMatch = true;
                break;
              }
            } finally {
              client.close();
            }
          } catch (error) {
            console.error(`Error checking potential match ${filePath}:`, error);
          }
        }
      }

      // If we found a match, continue to the next employer
      if (foundMatch) {
        validEmployers.push(employer);
        continue;
      }

      // No match found, remove the employer from the master DB
      console.log(
        `File not found for employer ${employer.id}, removing from master DB: ${employer.file_path}`,
      );
      // Add to the list of removed employers
      removedEmployers.push({
        id: employer.id,
        name: employer.display_name,
        filePath: employer.file_path,
      });

      // Use a non-blocking async operation to clean up the DB
      db.delete(employers)
        .where(eq(employers.id, employer.id))
        .run()
        .catch((err) =>
          console.error(
            `Error removing employer ${employer.id} from master DB:`,
            err,
          ),
        );
      continue; // Skip to next employer
    }

    // File exists, check if display name matches file name
    const fileName = path.basename(employer.file_path, EMPLOYER_DB_EXTENSION);

    if (employer.display_name !== fileName) {
      // Display name doesn't match file name, update it
      console.log(
        `Updating display name for employer ${employer.id} from "${employer.display_name}" to "${fileName}"`,
      );

      // Add to the list of updated employers
      updatedEmployers.push({
        id: employer.id,
        oldName: employer.display_name,
        newName: fileName,
      });

      // Update the display name in the database
      db.update(employers)
        .set({ display_name: fileName })
        .where(eq(employers.id, employer.id))
        .run()
        .catch((err) =>
          console.error(
            `Error updating display name for employer ${employer.id}:`,
            err,
          ),
        );

      // Update the display name in the current record for the response
      employer.display_name = fileName;
    }

    // Add to valid employers
    validEmployers.push(employer);
  }

  return {
    employers: validEmployers,
    removedEmployers: removedEmployers,
    updatedEmployers: updatedEmployers,
  };
});

// IPC handler for adding an existing employer DB
ipcMain.handle("employer:add-existing", async (_, filePath) => {
  // If no file path is provided, open a file picker
  if (!filePath) {
    const selectedPaths = await openEmployerFilePicker();
    if (!selectedPaths) {
      return { success: false, error: "No files selected." };
    }

    // Process multiple files
    if (Array.isArray(selectedPaths)) {
      const results: Array<{ id: string; name: string; filePath: string }> = [];
      const errors: Array<{ path: string; error: string | undefined }> = [];

      // Process each file
      for (const path of selectedPaths) {
        const result = await addExistingEmployer(path);
        if (result.success && result.employer) {
          results.push(result.employer);
        } else {
          errors.push({ path, error: result.error || "Unknown error" });
        }
      }

      return {
        success: true,
        multipleFiles: true,
        results,
        errors,
        totalAdded: results.length,
        totalErrors: errors.length,
        totalFiles: selectedPaths.length,
      };
    } else {
      // Single file path (shouldn't happen with new implementation, but kept for backward compatibility)
      filePath = selectedPaths;
    }
  }

  // Handle single file path (direct API call with path)
  return await addExistingEmployer(filePath);
});

// IPC handler for removing an employer
ipcMain.handle("employer:remove", async (_, employerId) => {
  return await removeEmployer(employerId);
});

async function createMainWindow() {
  // Ensure database is initialized before creating window
  await initializeDatabase();

  mainWindow = new BrowserWindow({
    title: APP_NAME,
    width: 1200,
    height: 800,
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, "../preload/index.js"),
    },
  });

  if (process.env.NODE_ENV === "development") {
    mainWindow.loadURL("http://localhost:3000");
  } else {
    mainWindow.loadFile(path.join(__dirname, "../../..", "out", "index.html"));
  }

  if (mainWindow) {
    mainWindow.on("closed", () => {
      mainWindow = null;
    });
  }
}

app.whenReady().then(createMainWindow);

import { getMasterDbClient } from "../../drizzle/adapters/master";

app.on("before-quit", () => {
  // Close all employer DBs
  closeAllEmployerDbs();
  // Close master DB
  const client = getMasterDbClient();
  if (client && typeof client.close === "function") {
    try {
      client.close();
      console.log("[MAIN] Master DB client closed on app quit.");
    } catch (e) {
      console.warn("[MAIN] Error closing master DB client on quit:", e);
    }
  }
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  if (!mainWindow) createMainWindow();
});
