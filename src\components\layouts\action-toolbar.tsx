"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useEffect } from "react";
import { useTaxYear } from "@/providers/tax-year-provider";
import { useNavigationStore } from "@/store/navigation-store";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Building2,
  Users,
  FileText,
  Calculator,
  CalendarFold,
  FileSpreadsheet,
  Download,
  Printer,
  ChevronRight,
  PinIcon,
  EyeOffIcon,
  CheckCircle,
  RotateCcw,
  CircleArrowLeft,
  CircleArrowRight,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ActionButton {
  id: string;
  label: string;
  icon: React.ReactNode;
  action?: () => void;
  href?: string;
  iconClassName?: string; // Added for individual icon coloring
  disabled?: boolean; // Added for conditional disabling
}

interface RouteConfig {
  actions: ActionButton[];
  taxYearSelector?: boolean;
}

// Configuration for each section (no leading slash)
import { ResetPayPeriodsDialog } from "@/components/payroll/pay-periods/ResetPayPeriodsDialog";
import {
  usePayPeriodsQuery,
  useDeletePayPeriodMutation,
  useDeletePayPeriodsByTypeAndNameMutation,
} from "@/hooks/tanstack-query/usePayPeriods";
import {
  useDeletePayPeriodScheduleMutation,
  usePayPeriodSchedules,
} from "@/hooks/tanstack-query/usePayPeriodSchedules";
import FinalisePayslipsModal from "../payroll/modals/FinalisePayslipsModal";
import ReopenPayslipsModal from "../payroll/modals/ReopenPayslipsModal";
import { useEmployeesForPeriod } from "@/hooks/tanstack-query/useEmployeesForPeriod";

// Storage key for payroll state (same as PayrollManagement)
const PAYROLL_STATE_KEY = `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_payroll_state`;

export function ActionToolbar() {
  const { globalSection, activeEmployerId, employerSections } =
    useNavigationStore();
  const { taxYear, setTaxYear } = useTaxYear();
  const [isPeriodSelectorPinned, setIsPeriodSelectorPinned] = useState(true);

  // State to track the currently selected period from PayrollManagement
  const [selectedPeriodId, setSelectedPeriodId] = useState<string | null>(null);

  // --- Reset Pay Periods Dialog State ---
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const { data: payPeriods = [] } = usePayPeriodsQuery();
  const deletePayPeriodMutation = useDeletePayPeriodMutation();
  const deletePayPeriodsByTypeAndNameMutation =
    useDeletePayPeriodsByTypeAndNameMutation();

  // Fetch pay period schedules and build mapping from type to label

  // --- Schedules ---
  const { data: schedules } = usePayPeriodSchedules(taxYear);
  // scheduleLabels is not needed for reset dialog

  // Find the active pay period (assume only one can be active)
  const activePeriod = payPeriods.find((p) => p.active);

  // Find the currently selected period in the UI (for reopen functionality)
  const selectedPeriod = selectedPeriodId
    ? payPeriods.find((p) => p.id === selectedPeriodId)
    : null;
  // periodType and scheduleLabel are no longer needed for reset logic

  // Get employees for the selected period to determine button states
  const employeesForPeriod = useEmployeesForPeriod(selectedPeriodId || "");

  // Determine button states based on payslip statuses
  const hasOpenPayslips = employeesForPeriod.some(
    (emp) => emp.status === "open",
  );
  const hasClosedPayslips = employeesForPeriod.some(
    (emp) => emp.status === "closed",
  );

  // Handler for reset action
  const handleResetPayPeriods = () => {
    setResetDialogOpen(true);
  };

  // Build schedules array for the dialog
  const scheduleOptions = Array.isArray(schedules)
    ? schedules.map((sched: any) => ({
        id: sched.id,
        label: sched.label,
        type: sched.type,
      }))
    : [];

  // Handler for confirming reset with selected schedule
  const deletePayPeriodScheduleMutation = useDeletePayPeriodScheduleMutation();
  const handleConfirmReset = async (scheduleId: string) => {
    const schedule = scheduleOptions.find((s) => s.id === scheduleId);
    if (schedule) {
      try {
        await deletePayPeriodsByTypeAndNameMutation.mutateAsync({
          type: schedule.type,
          scheduleId: schedule.id,
        });
        await deletePayPeriodScheduleMutation.mutateAsync(schedule.id);
      } catch (err) {
        // Optionally: show error to user
        console.error("Error resetting pay period schedule:", err);
      } finally {
        setResetDialogOpen(false);
      }
    }
  };

  const handleCancelReset = () => setResetDialogOpen(false);

  // Finalise/Reopen modal state
  const [finaliseModalOpen, setFinaliseModalOpen] = useState(false);
  const [reopenModalOpen, setReopenModalOpen] = useState(false);

  const handleOpenFinalise = () => setFinaliseModalOpen(true);

  const handleOpenReopen = () => setReopenModalOpen(true);

  // Navigation functions for pay periods
  const navigateToPreviousPeriod = () => {
    if (!selectedPeriodId || !payPeriods.length) return;

    // Find current period and get the previous one by period_number
    const currentPeriod = payPeriods.find((p) => p.id === selectedPeriodId);
    if (!currentPeriod) return;

    // Get all periods for the same tax year and schedule, sorted by period_number
    const sameTaxYearPeriods = payPeriods
      .filter(
        (p) =>
          p.tax_year === currentPeriod.tax_year &&
          p.schedule_id === currentPeriod.schedule_id,
      )
      .sort((a, b) => a.period_number - b.period_number);

    const currentIndex = sameTaxYearPeriods.findIndex(
      (p) => p.id === selectedPeriodId,
    );
    if (currentIndex > 0) {
      const previousPeriod = sameTaxYearPeriods[currentIndex - 1];
      updatePayrollActivePeriod(previousPeriod.id);
    }
  };

  const navigateToNextPeriod = () => {
    if (!selectedPeriodId || !payPeriods.length) return;

    // Find current period and get the next one by period_number
    const currentPeriod = payPeriods.find((p) => p.id === selectedPeriodId);
    if (!currentPeriod) return;

    // Get all periods for the same tax year and schedule, sorted by period_number
    const sameTaxYearPeriods = payPeriods
      .filter(
        (p) =>
          p.tax_year === currentPeriod.tax_year &&
          p.schedule_id === currentPeriod.schedule_id,
      )
      .sort((a, b) => a.period_number - b.period_number);

    const currentIndex = sameTaxYearPeriods.findIndex(
      (p) => p.id === selectedPeriodId,
    );
    if (currentIndex >= 0 && currentIndex < sameTaxYearPeriods.length - 1) {
      const nextPeriod = sameTaxYearPeriods[currentIndex + 1];
      updatePayrollActivePeriod(nextPeriod.id);
    }
  };

  // Helper function to update the active period in PayrollManagement
  const updatePayrollActivePeriod = (periodId: string) => {
    try {
      const savedState = localStorage.getItem(PAYROLL_STATE_KEY);
      const currentState = savedState ? JSON.parse(savedState) : {};

      const newState = {
        ...currentState,
        activePeriod: periodId,
      };

      localStorage.setItem(PAYROLL_STATE_KEY, JSON.stringify(newState));

      // Dispatch custom event to notify PayrollManagement of the change
      const event = new CustomEvent("payrollStateChange", {
        detail: { activePeriod: periodId },
      });
      window.dispatchEvent(event);

      // Update local state to reflect the change
      setSelectedPeriodId(periodId);
    } catch (error) {
      console.error("Error updating active period:", error);
    }
  };

  // Check if navigation buttons should be enabled
  const canNavigatePrevious = () => {
    if (!selectedPeriodId || !payPeriods.length) return false;
    const currentPeriod = payPeriods.find((p) => p.id === selectedPeriodId);
    if (!currentPeriod) return false;

    const sameTaxYearPeriods = payPeriods
      .filter(
        (p) =>
          p.tax_year === currentPeriod.tax_year &&
          p.schedule_id === currentPeriod.schedule_id,
      )
      .sort((a, b) => a.period_number - b.period_number);

    const currentIndex = sameTaxYearPeriods.findIndex(
      (p) => p.id === selectedPeriodId,
    );
    return currentIndex > 0;
  };

  const canNavigateNext = () => {
    if (!selectedPeriodId || !payPeriods.length) return false;
    const currentPeriod = payPeriods.find((p) => p.id === selectedPeriodId);
    if (!currentPeriod) return false;

    const sameTaxYearPeriods = payPeriods
      .filter(
        (p) =>
          p.tax_year === currentPeriod.tax_year &&
          p.schedule_id === currentPeriod.schedule_id,
      )
      .sort((a, b) => a.period_number - b.period_number);

    const currentIndex = sameTaxYearPeriods.findIndex(
      (p) => p.id === selectedPeriodId,
    );
    return currentIndex >= 0 && currentIndex < sameTaxYearPeriods.length - 1;
  };

  // Load saved pin state from localStorage on mount
  useEffect(() => {
    const savedPinState = localStorage.getItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
    );
    if (savedPinState !== null) {
      setIsPeriodSelectorPinned(savedPinState === "true");
    }
  }, []);

  // Read the currently selected period from PayrollManagement's localStorage
  useEffect(() => {
    const readSelectedPeriod = () => {
      try {
        const savedState = localStorage.getItem(PAYROLL_STATE_KEY);
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          setSelectedPeriodId(parsedState.activePeriod || null);
        }
      } catch (error) {
        console.error(
          "Error reading selected period from localStorage:",
          error,
        );
      }
    };

    // Read initial value
    readSelectedPeriod();

    // Listen for storage changes to keep in sync
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === PAYROLL_STATE_KEY) {
        readSelectedPeriod();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    // Also listen for custom events from PayrollManagement
    const handlePayrollStateChange = () => {
      readSelectedPeriod();
    };

    window.addEventListener("payrollStateChange", handlePayrollStateChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener(
        "payrollStateChange",
        handlePayrollStateChange,
      );
    };
  }, []);

  // Save pin state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
      isPeriodSelectorPinned.toString(),
    );
  }, [isPeriodSelectorPinned]);

  const togglePeriodSelectorPin = () => {
    const newValue = !isPeriodSelectorPinned;
    setIsPeriodSelectorPinned(newValue);
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_pinned`,
      newValue.toString(),
    );
    // Dispatch event so selector updates immediately
    const event = new CustomEvent("periodSelectorVisibilityChange", {
      detail: { visible: newValue },
    });
    window.dispatchEvent(event);
  };

  // Handle hover events for the pay periods toggle button
  const handleButtonMouseEnter = () => {
    // When hovering over the button, temporarily show the pay periods selector
    // by setting a temporary localStorage value
    localStorage.setItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`,
      "true",
    );
  };

  const handleButtonMouseLeave = () => {
    // When leaving the button, remove the temporary visibility flag
    localStorage.removeItem(
      `${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_temp_visible`,
    );
  };

  // Determine current section
  let section = globalSection;
  if (activeEmployerId && employerSections[activeEmployerId]) {
    section = employerSections[activeEmployerId];
  }

  const sectionConfigs: Record<string, RouteConfig> = {
    payroll: {
      actions: [
        {
          id: "previous-period",
          label: "Previous Period",
          icon: <CircleArrowLeft className="size-6" />,
          iconClassName: "text-blue-500 dark:text-blue-400",
          action: navigateToPreviousPeriod,
          disabled: !canNavigatePrevious(),
        },
        {
          id: "next-period",
          label: "Next Period",
          icon: <CircleArrowRight className="size-6" />,
          iconClassName: "text-blue-500 dark:text-blue-400",
          action: navigateToNextPeriod,
          disabled: !canNavigateNext(),
        },
        {
          id: "close-payslips",
          label: "Close Payslips",
          icon: <CheckCircle className="size-6" />,
          iconClassName: "text-green-500 dark:text-green-400",
          action: handleOpenFinalise,
          disabled: !hasOpenPayslips || !selectedPeriod,
        },
        {
          id: "reopen-payslips",
          label: "Reopen Payslips",
          icon: <RotateCcw className="size-6" />,
          iconClassName: "text-yellow-500 dark:text-yellow-400",
          action: handleOpenReopen,
          disabled: !hasClosedPayslips || !selectedPeriod,
        },
        {
          id: "reset-pay-periods",
          label: "Reset Pay Periods",
          icon: <EyeOffIcon className="size-6" />,
          iconClassName: "text-red-500 dark:text-red-400",
          action: handleResetPayPeriods,
        },
        {
          id: "pay-calendar",
          label: "Calendar",
          icon: <CalendarFold className="size-6" />,
          iconClassName: "text-sky-500 dark:text-sky-400",
        },
        {
          id: "reports",
          label: "Reports",
          icon: <FileText className="size-6" />,
          iconClassName: "text-amber-500 dark:text-amber-400",
        },
      ],
      taxYearSelector: true, // Only show tax year selector on payroll pages
    },
    employees: {
      actions: [
        {
          id: "new-employee",
          label: "New",
          icon: <Users className="size-6" />,
          iconClassName: "text-blue-500 dark:text-blue-400",
        },
        {
          id: "import",
          label: "Import",
          icon: <Download className="size-6" />,
          iconClassName: "text-violet-500 dark:text-violet-400",
        },
        {
          id: "export",
          label: "Export",
          icon: <FileSpreadsheet className="size-6" />,
          iconClassName: "text-emerald-500 dark:text-emerald-400",
        },
        {
          id: "print",
          label: "Print",
          icon: <Printer className="size-6" />,
          iconClassName: "text-slate-500 dark:text-slate-400",
        },
      ],
    },
    employer: {
      actions: [
        {
          id: "new-employer",
          label: "New",
          icon: <Building2 className="size-6" />,
          iconClassName: "text-indigo-500 dark:text-indigo-400",
        },
        {
          id: "import",
          label: "Import",
          icon: <Download className="size-6" />,
          iconClassName: "text-violet-500 dark:text-violet-400",
        },
        {
          id: "export",
          label: "Export",
          icon: <FileSpreadsheet className="size-6" />,
          iconClassName: "text-emerald-500 dark:text-emerald-400",
        },
      ],
    },
    dashboard: { actions: [] },
  };
  // Get config for section, or fallback to empty actions
  const sectionConfig = sectionConfigs[section] || { actions: [] };

  const { actions, taxYearSelector } = sectionConfig;

  if (!actions.length) return null;

  return (
    <div className="w-full items-center pt-2 shadow-slate-500">
      <div className="mt-1 flex h-12 items-center px-4 align-middle">
        {/* Left section - Tax Year Selector */}
        <div className="flex-1">
          {taxYearSelector ? (
            <div className="flex items-center">
              <Select value={taxYear} onValueChange={setTaxYear}>
                <SelectTrigger className="w-[120px] font-semibold text-pink-500">
                  <SelectValue placeholder="Tax Year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2024/25">2024/25</SelectItem>
                  <SelectItem value="2025/26">2025/26</SelectItem>
                </SelectContent>
              </Select>
              <span className="ml-2 text-sm font-medium text-sky-500 italic">
                Tax Year
              </span>

              {/* Pay Periods View Toggle */}
              <div className="ml-6 flex items-center gap-2">
                <Button
                  id="pay-periods-toggle-button"
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-1 rounded-md border px-2 py-1 text-xs font-medium hover:bg-slate-100 dark:hover:bg-zinc-800"
                  onClick={togglePeriodSelectorPin}
                  onMouseEnter={handleButtonMouseEnter}
                  onMouseLeave={handleButtonMouseLeave}
                >
                  {isPeriodSelectorPinned ? (
                    <>
                      <PinIcon className="h-4 w-4 text-sky-500" />
                      <span>Pay periods view</span>
                    </>
                  ) : (
                    <>
                      <EyeOffIcon className="h-4 w-4 text-slate-500" />
                      <span>Pay periods view</span>
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div></div>
          )}
        </div>

        {/* Action buttons in the center */}
        <div className="flex flex-1 items-center justify-center">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className={cn(
                "flex flex-col items-center justify-center px-3 text-sm font-normal transition-all",
                "hover:bg-transparent focus:ring-0 focus:outline-none",
                action.disabled && "cursor-not-allowed opacity-50",
              )}
              onClick={action.disabled ? undefined : action.action}
              disabled={action.disabled}
            >
              <div
                className={cn(
                  "flex items-center justify-center",
                  action.iconClassName,
                  action.disabled && "text-gray-400 dark:text-gray-600",
                )}
              >
                {action.icon}
              </div>
              <span className="w-full text-center text-xs">{action.label}</span>
            </Button>
          ))}
        </div>

        {/* Right section - Empty div to balance the layout */}
        <div className="flex flex-1 justify-end">
          <div className="w-[120px]"></div>
        </div>
      </div>
      <ResetPayPeriodsDialog
        open={resetDialogOpen}
        schedules={scheduleOptions}
        onOpenChange={setResetDialogOpen}
        onCancel={handleCancelReset}
        onReset={handleConfirmReset}
      />
      {selectedPeriod && (
        <FinalisePayslipsModal
          open={finaliseModalOpen}
          onOpenChange={setFinaliseModalOpen}
          periodId={selectedPeriod.id}
        />
      )}
      {selectedPeriod && (
        <ReopenPayslipsModal
          open={reopenModalOpen}
          onOpenChange={setReopenModalOpen}
          periodId={selectedPeriod.id}
        />
      )}
    </div>
  );
}
