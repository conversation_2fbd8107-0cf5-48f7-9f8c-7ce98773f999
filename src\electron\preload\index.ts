import { contextBridge, ipc<PERSON>enderer } from "electron";
import type { CreateEmployerDbParams } from "../file-handlers/createEmployerDb";

contextBridge.exposeInMainWorld("api", {
  createEmployerDb: async (params: CreateEmployerDbParams) => {
    return await ipcRenderer.invoke("employer:create-db", params);
  },
  getEmployers: async () => {
    return await ipcRenderer.invoke("employers:list");
  },
  addExistingEmployer: async (filePath: string) => {
    return await ipcRenderer.invoke("employer:add-existing", filePath);
  },
  removeEmployer: async (employerId: string) => {
    return await ipcRenderer.invoke("employer:remove", employerId);
  },
  getEmployer: async (dbPath: string) => {
    return await ipcRenderer.invoke("employer:get", dbPath);
  },
  updateEmployer: async (dbPath: string, employer: any) => {
    return await ipcRenderer.invoke("employer:update", dbPath, employer);
  },
  invoke: (channel: string, ...args: any[]) =>
    ipcRenderer.invoke(channel, ...args),
  // Debug logging
  debugLog: (message: string) => ipcRenderer.invoke("debug:log", message),
  // Add more safe APIs here as needed
});
