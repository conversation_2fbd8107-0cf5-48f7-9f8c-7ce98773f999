import { ipcMain } from "electron";
import { getEmployerDb } from "../main/EmployerDbManager";
import * as schema from "../../drizzle/schema/employer/payslip";
import { inArray, eq, and } from "drizzle-orm";
import crypto from "crypto";
import { payPeriods } from "../../drizzle/schema/employer/payPeriod";

// Fetch a payslip (may not exist yet)
ipcMain.handle(
  "employerDb:getPayslip",
  async (_event, dbPath: string, employeeId: string, periodId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;

      // Use AND condition instead of chaining .where() calls
      const [payslip] = await db
        .select()
        .from(schema.payslips)
        .where(
          and(
            eq(schema.payslips.employee_id, employeeId),
            eq(schema.payslips.period_id, periodId),
          ),
        )
        .all();

      if (!payslip) {
        return {
          success: true,
          payslip: null,
          items: [],
          notes: [],
          additions: [],
          deductions: [],
        };
      }
      const items = await db
        .select()
        .from(schema.payslipLineItems)
        .where(eq(schema.payslipLineItems.payslip_id, payslip.id))
        .all();
      const notes = await db
        .select()
        .from(schema.payslipNotes)
        .where(eq(schema.payslipNotes.payslip_id, payslip.id))
        .all();
      const additions = await db
        .select()
        .from(schema.payslipAdditionsLineItems)
        .where(eq(schema.payslipAdditionsLineItems.payslip_id, payslip.id))
        .all();
      const deductions = await db
        .select()
        .from(schema.payslipDeductionsLineItems)
        .where(eq(schema.payslipDeductionsLineItems.payslip_id, payslip.id))
        .all();

      return { success: true, payslip, items, notes, additions, deductions };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Fetch all item types
ipcMain.handle("employerDb:getItemTypes", async (_event, dbPath: string) => {
  try {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    let itemTypes = await db.select().from(schema.payslipItemTypes).all();
    // Seed default types if none exist
    if (itemTypes.length === 0) {
      const now = Math.floor(Date.now() / 1000);
      const defaults = [
        {
          id: "salary",
          code: "salary",
          section: "basic",
          display_label: "Salary",
          default_units: null,
          created_at: now,
          updated_at: now,
        },
        {
          id: "daily",
          code: "daily",
          section: "basic",
          display_label: "Daily",
          default_units: null,
          created_at: now,
          updated_at: now,
        },
        {
          id: "hourly",
          code: "hourly",
          section: "basic",
          display_label: "Hourly",
          default_units: null,
          created_at: now,
          updated_at: now,
        },
      ];
      await db.insert(schema.payslipItemTypes).values(defaults).run();
      itemTypes = await db.select().from(schema.payslipItemTypes).all();
    }
    return { success: true, itemTypes };
  } catch (err: any) {
    return { success: false, error: err.message };
  }
});

// Upsert a line item
ipcMain.handle(
  "employerDb:upsertPayslipLineItem",
  async (_event, dbPath: string, item: any) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const now = Math.floor(Date.now() / 1000);
      if (!item.id) {
        const id = crypto.randomUUID();
        const [inserted] = await db
          .insert(schema.payslipLineItems)
          .values({ ...item, id, created_at: now, updated_at: now })
          .returning();
        return { success: true, lineItem: inserted };
      } else {
        const [updated] = await db
          .update(schema.payslipLineItems)
          .set({ ...item, updated_at: now })
          .where(eq(schema.payslipLineItems.id, item.id))
          .returning();
        return { success: true, lineItem: updated };
      }
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete a line item
ipcMain.handle(
  "employerDb:deletePayslipLineItem",
  async (_event, dbPath: string, id: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipLineItems)
        .where(eq(schema.payslipLineItems.id, id));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Upsert a payslip note
ipcMain.handle(
  "employerDb:upsertPayslipNote",
  async (_event, dbPath: string, note: any) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const now = Math.floor(Date.now() / 1000);

      if (!note.id) {
        // Create new note
        const id = crypto.randomUUID();
        const [inserted] = await db
          .insert(schema.payslipNotes)
          .values({
            ...note,
            id,
            created_at: now,
            updated_at: now,
          })
          .returning();
        return { success: true, note: inserted };
      } else {
        // Update existing note
        const [updated] = await db
          .update(schema.payslipNotes)
          .set({ ...note, updated_at: now })
          .where(eq(schema.payslipNotes.id, note.id))
          .returning();
        return { success: true, note: updated };
      }
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Add a payslip note (legacy - keeping for compatibility)
ipcMain.handle(
  "employerDb:addPayslipNote",
  async (_event, dbPath: string, payslipId: string, content: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const id = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [note] = await db
        .insert(schema.payslipNotes)
        .values({
          id,
          payslip_id: payslipId,
          content,
          show_on_payslip: true,
          is_repeating: false,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return { success: true, note };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Delete a payslip note
ipcMain.handle(
  "employerDb:deletePayslipNote",
  async (_event, dbPath: string, noteId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const result = await db
        .delete(schema.payslipNotes)
        .where(eq(schema.payslipNotes.id, noteId));
      return { success: true, deletedCount: result.changes ?? 0 };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Create a new payslip record
ipcMain.handle(
  "employerDb:createPayslip",
  async (_event, dbPath: string, employeeId: string, periodId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const id = crypto.randomUUID();
      const now = Math.floor(Date.now() / 1000);
      const [inserted] = await db
        .insert(schema.payslips)
        .values({
          id,
          employee_id: employeeId,
          period_id: periodId,
          created_at: now,
          updated_at: now,
        })
        .returning();
      return { success: true, payslip: inserted };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Finalise selected payslips and open next period
ipcMain.handle(
  "employerDb:finalisePayslips",
  async (_event, dbPath: string, periodId: string, employeeIds: string[]) => {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;
    try {
      await db.transaction(async (tx: any) => {
        const now = Math.floor(Date.now() / 1000);
        const payslipTable = schema.payslips;
        // Ensure payslips exist for each employee
        for (const empId of employeeIds) {
          const existing = await tx
            .select()
            .from(payslipTable)
            .where(eq(payslipTable.employee_id, empId))
            .where(eq(payslipTable.period_id, periodId))
            .get();
          if (!existing) {
            const id = crypto.randomUUID();
            await tx
              .insert(payslipTable)
              .values({
                id,
                employee_id: empId,
                period_id: periodId,
                created_at: now,
                updated_at: now,
              })
              .run();
          }
        }
        // Close payslips by employee
        await tx
          .update(payslipTable)
          .set({
            status: "closed",
            finalised_at: now.toString(),
            updated_at: now,
          })
          .where(
            and(
              inArray(payslipTable.employee_id, employeeIds),
              eq(payslipTable.period_id, periodId),
            ),
          )
          .run();

        // Open next pay period
        const current = await tx
          .select()
          .from(payPeriods)
          .where(eq(payPeriods.id, periodId))
          .get();
        if (current) {
          const { schedule_id, tax_year } = current;
          // Determine next period by ordering schedule periods
          const allPeriods = await tx
            .select()
            .from(payPeriods)
            .where(eq(payPeriods.schedule_id, schedule_id))
            .where(eq(payPeriods.tax_year, tax_year))
            .orderBy(payPeriods.period_number)
            .all();
          const idx = allPeriods.findIndex((p: any) => p.id === periodId);
          const next =
            idx >= 0 && idx < allPeriods.length - 1
              ? allPeriods[idx + 1]
              : undefined;
          if (next) {
            // Set current period inactive
            await tx
              .update(payPeriods)
              .set({ active: 0, updated_at: now })
              .where(eq(payPeriods.id, periodId))
              .run();
            // Set next period active
            await tx
              .update(payPeriods)
              .set({ active: 1, updated_at: now })
              .where(eq(payPeriods.id, next.id))
              .run();
            // Ensure payslips exist for next period
            for (const empId of employeeIds) {
              const existsNext = await tx
                .select()
                .from(payslipTable)
                .where(eq(payslipTable.employee_id, empId))
                .where(eq(payslipTable.period_id, next.id))
                .get();
              if (!existsNext) {
                const newId = crypto.randomUUID();
                await tx
                  .insert(payslipTable)
                  .values({
                    id: newId,
                    employee_id: empId,
                    period_id: next.id,
                    created_at: now,
                    updated_at: now,
                  })
                  .run();
              }
            }

            // First, clear all existing items from next period payslips for all employees
            for (const empId of employeeIds) {
              const nextPayslip = await tx
                .select()
                .from(payslipTable)
                .where(
                  and(
                    eq(payslipTable.employee_id, empId),
                    eq(payslipTable.period_id, next.id),
                  ),
                )
                .get();

              if (nextPayslip) {
                // Clear existing items
                await tx
                  .delete(schema.payslipLineItems)
                  .where(eq(schema.payslipLineItems.payslip_id, nextPayslip.id))
                  .run();
                await tx
                  .delete(schema.payslipAdditionsLineItems)
                  .where(
                    eq(
                      schema.payslipAdditionsLineItems.payslip_id,
                      nextPayslip.id,
                    ),
                  )
                  .run();
                await tx
                  .delete(schema.payslipDeductionsLineItems)
                  .where(
                    eq(
                      schema.payslipDeductionsLineItems.payslip_id,
                      nextPayslip.id,
                    ),
                  )
                  .run();
                await tx
                  .delete(schema.payslipNotes)
                  .where(eq(schema.payslipNotes.payslip_id, nextPayslip.id))
                  .run();
              }
            }

            // Then copy repeating items from current period to next period
            for (const empId of employeeIds) {
              // Get current period payslip
              const currentPayslip = await tx
                .select()
                .from(payslipTable)
                .where(
                  and(
                    eq(payslipTable.employee_id, empId),
                    eq(payslipTable.period_id, periodId),
                  ),
                )
                .get();

              // Get next period payslip
              const nextPayslip = await tx
                .select()
                .from(payslipTable)
                .where(
                  and(
                    eq(payslipTable.employee_id, empId),
                    eq(payslipTable.period_id, next.id),
                  ),
                )
                .get();

              if (!currentPayslip || !nextPayslip) continue;

              // Copy repeating basic pay items
              const repeatingBasicItems = await tx
                .select()
                .from(schema.payslipLineItems)
                .where(
                  and(
                    eq(schema.payslipLineItems.payslip_id, currentPayslip.id),
                    eq(schema.payslipLineItems.is_repeating, true),
                  ),
                )
                .all();

              for (const item of repeatingBasicItems) {
                await tx
                  .insert(schema.payslipLineItems)
                  .values({
                    id: crypto.randomUUID(),
                    payslip_id: nextPayslip.id,
                    item_type_id: item.item_type_id,
                    units: item.zeroise_next ? 0 : item.units,
                    rate_or_amount: item.rate_or_amount, // Always preserve the rate
                    is_custom: item.is_custom,
                    is_repeating: true,
                    zeroise_next: item.zeroise_next, // Keep the same zeroise_next value
                    notes: item.notes,
                    created_at: now,
                    updated_at: now,
                  })
                  .run();
              }

              // Copy repeating additions
              const repeatingAdditions = await tx
                .select()
                .from(schema.payslipAdditionsLineItems)
                .where(
                  and(
                    eq(
                      schema.payslipAdditionsLineItems.payslip_id,
                      currentPayslip.id,
                    ),
                    eq(schema.payslipAdditionsLineItems.is_repeating, true),
                  ),
                )
                .all();

              for (const item of repeatingAdditions) {
                await tx
                  .insert(schema.payslipAdditionsLineItems)
                  .values({
                    id: crypto.randomUUID(),
                    payslip_id: nextPayslip.id,
                    item_type_id: item.item_type_id,
                    amount: item.zeroise_next ? 0 : item.amount,
                    is_repeating: true,
                    zeroise_next: item.zeroise_next, // Keep the same zeroise_next value
                    created_at: now,
                    updated_at: now,
                  })
                  .run();
              }

              // Copy repeating deductions
              const repeatingDeductions = await tx
                .select()
                .from(schema.payslipDeductionsLineItems)
                .where(
                  and(
                    eq(
                      schema.payslipDeductionsLineItems.payslip_id,
                      currentPayslip.id,
                    ),
                    eq(schema.payslipDeductionsLineItems.is_repeating, true),
                  ),
                )
                .all();

              for (const item of repeatingDeductions) {
                await tx
                  .insert(schema.payslipDeductionsLineItems)
                  .values({
                    id: crypto.randomUUID(),
                    payslip_id: nextPayslip.id,
                    item_type_id: item.item_type_id,
                    amount: item.zeroise_next ? 0 : item.amount,
                    is_repeating: true,
                    zeroise_next: item.zeroise_next, // Keep the same zeroise_next value
                    created_at: now,
                    updated_at: now,
                  })
                  .run();
              }

              // Copy repeating notes
              const repeatingNotes = await tx
                .select()
                .from(schema.payslipNotes)
                .where(
                  and(
                    eq(schema.payslipNotes.payslip_id, currentPayslip.id),
                    eq(schema.payslipNotes.is_repeating, true),
                  ),
                )
                .all();

              for (const note of repeatingNotes) {
                await tx
                  .insert(schema.payslipNotes)
                  .values({
                    id: crypto.randomUUID(),
                    payslip_id: nextPayslip.id,
                    content: note.content,
                    show_on_payslip: note.show_on_payslip,
                    is_repeating: true,
                    created_at: now,
                    updated_at: now,
                  })
                  .run();
              }
            }
            // Set payslips for next period to status 'open' for relevant employees
            await tx
              .update(payslipTable)
              .set({ status: "open", updated_at: now })
              .where(inArray(payslipTable.employee_id, employeeIds))
              .where(eq(payslipTable.period_id, next.id))
              .run();
          }
        }
      });
      return { success: true };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Get payslip statuses for all employees in a period
ipcMain.handle(
  "employerDb:getPayslipStatusesForPeriod",
  async (_event, dbPath: string, periodId: string) => {
    try {
      const entry = getEmployerDb(dbPath);
      if (!entry) return { success: false, error: "Employer DB is not open" };
      const db = entry.db;
      const payslips = await db
        .select({
          employee_id: schema.payslips.employee_id,
          status: schema.payslips.status,
        })
        .from(schema.payslips)
        .where(eq(schema.payslips.period_id, periodId))
        .all();
      return { success: true, payslips };
    } catch (err: any) {
      return { success: false, error: err.message };
    }
  },
);

// Reopen selected payslips
ipcMain.handle(
  "employerDb:reopenPayslips",
  async (_event, dbPath: string, periodId: string, employeeIds: string[]) => {
    const entry = getEmployerDb(dbPath);
    if (!entry) return { success: false, error: "Employer DB is not open" };
    const db = entry.db;

    // Validate inputs
    if (!periodId || !Array.isArray(employeeIds) || employeeIds.length === 0) {
      return {
        success: false,
        error: "Invalid parameters: periodId and employeeIds are required",
      };
    }

    try {
      const result = await db.transaction(async (tx: any) => {
        const now = Math.floor(Date.now() / 1000);
        const payslipTable = schema.payslips;

        // Ensure payslips exist for each employee in this period
        for (const empId of employeeIds) {
          if (!empId || typeof empId !== "string") {
            throw new Error(`Invalid employee ID: ${empId}`);
          }

          const existing = await tx
            .select()
            .from(payslipTable)
            .where(eq(payslipTable.employee_id, empId))
            .where(eq(payslipTable.period_id, periodId))
            .get();
          if (!existing) {
            const id = crypto.randomUUID();
            await tx
              .insert(payslipTable)
              .values({
                id,
                employee_id: empId,
                period_id: periodId,
                status: "open",
                created_at: now,
                updated_at: now,
              })
              .run();
          }
        }

        // Reopen payslips by setting status to 'open' and clearing finalised_at
        const updateResult = await tx
          .update(payslipTable)
          .set({ status: "open", finalised_at: null, updated_at: now })
          .where(
            and(
              inArray(payslipTable.employee_id, employeeIds),
              eq(payslipTable.period_id, periodId),
            ),
          )
          .run();

        return { success: true, updatedCount: updateResult.changes || 0 };
      });

      return result;
    } catch (err: any) {
      console.error("[IPC] Error reopening payslips:", err);
      return { success: false, error: err.message || "Unknown error occurred" };
    }
  },
);
