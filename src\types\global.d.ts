// Global type definitions for window.api (Electron preload)
// This file is automatically included by tsconfig.json (if in src or root)
import type {
  CreateEmployerDbParams,
  CreateEmployerDbResult,
} from "@/electron/preload/types";

// Define interfaces for the new API methods
interface AddExistingEmployerResult {
  success: boolean;
  error?: string;
  employer?: {
    id: string;
    name: string;
    filePath: string;
  };
}

interface RemoveEmployerResult {
  success: boolean;
  error?: string;
}

declare global {
  interface Window {
    api: {
      createEmployerDb: (
        params: CreateEmployerDbParams,
      ) => Promise<CreateEmployerDbResult>;
      getEmployers: () => Promise<any[]>;
      addExistingEmployer: (
        filePath: string,
      ) => Promise<AddExistingEmployerResult>;
      removeEmployer: (employerId: string) => Promise<RemoveEmployerResult>;
      // Add invoke for IPC (channel, ...args)
      invoke: <T = any>(channel: string, ...args: any[]) => Promise<T>;
      // Debug logging
      debugLog: (message: string) => Promise<boolean>;
    };
  }
}

export {}; // Required for global augmentation
